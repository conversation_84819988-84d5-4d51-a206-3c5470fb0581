---
import type { Project } from "../../data/types";

export interface Props {
  project: Project;
  lang: "fr" | "en";
}
const { project, lang } = Astro.props;
const t = (obj: { fr: string; en: string }) => obj[lang];
---

<article class="project-card">
  <!-- Texte -->
  <div class="content">
    <div class="icon">
    
    </div>
    <h3 class="title">{t(project.title)}</h3>
    <p class="desc">{t(project.description)}</p>

    <ul class="tags">
      {project.tech.map((tag) => <li class="tag">{tag}</li>)}
    </ul>

    <div class="actions">
      {
        project.links.github && (
          <a
            class="btn ghost"
            href={project.links.github}
            target="_blank"
            rel="noopener"
          >
            GitHub
          </a>
        )
      }
      {
        project.links.demo && (
          <a
            class="btn btn--primary"
            href={project.links.demo}
            target="_blank"
            rel="noopener"
          >
            Live view →
          </a>
        )
      }
    </div>
  </div>

  <!-- Image -->
  <div class="thumb">
    <img
      src={project.cover}
      alt={t(project.title)}
      loading="lazy"
      decoding="async"
    />
  </div>
</article>

<style>
  .project-card {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 2rem;
    align-items: center;
    background: var(--color-surface, var(--color-background));
    border: 1px solid var(--color-border, rgba(127, 127, 127, 0.15));
    border-radius: var(--border-radius);
    padding: var(--spacing-5);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    text-align: left;
    gap: var(--spacing-3);
    grid-column: 1;
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }

  .icon img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    object-position: center;
    border-radius: var(--border-radius);
  }

  .title {
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0;
  }
  .desc {
    margin: 0;
    color: var(--color-text-secondary);
    line-height: 1.5;
  }

  .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0;
    margin: 0.5rem 0 0;
    list-style: none;
  }
  .tag {
    font-size: 0.85rem;
    padding: 0.25rem 0.55rem;
    border-radius: 999px;
    background: color-mix(in srgb, var(--accent-regular) 8%, transparent);
    border: 1px solid color-mix(in srgb, var(--accent-regular) 20%, transparent);
  }

  .actions {
    display: flex;
    gap: 0.75rem;
    margin-top: auto;
  }

  .btn.ghost {
    background: transparent;
    border: 1px solid var(--color-border, rgba(127, 127, 127, 0.25));
    color: var(--color-text);
  }

  .thumb {
    height: 200px;
    width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    overflow: hidden;
    grid-column: 2;
  }

  .thumb img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 16 / 9;
  }

  /* Responsive */
  @media (max-width: 600px) {
    .project-card {
      grid-template-columns: 1fr;
      text-align: center;
    }
    .content {
      align-items: center;
    }
    .actions {
      justify-content: center;
    }
  }

  @media (max-width: 900px) {
    .project-card {
      grid-template-columns: 1fr;
      text-align: center;
    }
    .content {
      align-items: center;
    }
    .actions {
      justify-content: center;
    }
    .thumb {
      height: auto;
    }
    .thumb img {
      aspect-ratio: auto;
    }
  }
</style>
