---
import Icon from "../common/Icon.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { i18n } = Astro.props;
---

<section id="contact">
  <div class="title-wrapper">
    <h2 class="title no-select">{i18n.contact.title}</h2>
    <div class="line no-select"></div>
  </div>
  <div class="container">
    <p class="no-select">{i18n.contact.description}</p>
    <div class="contact-button">
      <a
        class="btn btn-linkedIn"
        href="https://www.linkedin.com/in/jimmy-gaucher-42175510a/"
      >
        <span class="sign"><Icon icon="linkedin" size="1.25rem" /></span>
        <span class="text">LinkedIn</span>
      </a>
      <a class="btn btn-mail" href="mailto:<EMAIL>">
        <span class="sign"><Icon icon="mail" size="1.25rem" /></span>
        <span class="text">Email</span>
      </a>
    </div>
  </div>
</section>

<style>
  #contact {
    padding: var(--spacing-5) var(--spacing-4);
    background: var(--color-background);
    text-align: center;
  }

  .title-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    max-width: var(--container-max-width, 1100px);
    margin: 0 auto;
    padding: 0 var(--spacing-4, 1rem);
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-5);
  }

  .title {
    color: var(--color-text-primary);
    margin: 0;
  }

  .line {
    width: 100%;
    max-width: 300px;
    height: 4px;
    border-radius: var(--border-radius);
    background: var(--accent-regular);
  }

  .container {
    max-width: var(--container-max-width, 1100px);
    margin: 0 auto;
    padding: 0 var(--spacing-4, 1rem);
  }

  p {
    color: var(--color-text-secondary);
  }

  .contact-button {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    margin-top: var(--spacing-5);
  }

  .btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-4);
    height: 3rem;
    border-radius: var(--border-radius);
    background: var(--accent-regular);
    color: var(--accent-text-over);
    text-decoration: none;
    font-weight: 600;
    transition: transform 0.2s ease;
  }
  @media (max-width: 900px) {
    .container {
      padding: 0 var(--spacing-3);
    }
  }
</style>
