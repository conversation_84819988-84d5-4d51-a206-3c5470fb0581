---
import Image from "astro/components/Image.astro";
import rouroux from "../../assets/images/rouroux-ghibli.webp";
import Icon from "../common/Icon.astro";
import ResumeButton from "../common/ResumeButton.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
}
const { i18n } = Astro.props;
---

<script is:inline></script>

<section id="about">
  <div class="title-wrapper">
    <h2 class="title no-select">{i18n.about.title}</h2>
    <div class="line no-select"></div>
  </div>
  <div class="container">
    <div class="col left">
      <p class="description no-select">{i18n.about.description}</p>
      <ResumeButton label={i18n.hero.cta_resume} />
    </div>
    <div class="col right">
      <div class="image-container">
        <Image
          class="about-image"
          src={rouroux}
          alt="Portrait de Rouroux"
          widths={[240, 320, 400]}
          sizes="(max-width: 900px) 70vw, 380px"
          loading="lazy"
          decoding="async"
        />
      </div>
    </div>
  </div>
</section>

<style>
  #about {
    padding: var(--spacing-5) var(--spacing-4);
    background: var(--color-background);
    text-align: center;
    max-width: var(--container-max-width, 1100px);
    margin: 0 auto;
  }

  .title-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    max-width: var(--container-max-width, 1100px);
    margin: 0 auto;
    padding: 0 var(--spacing-4, 1rem);
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-5);
  }

  .title {
    color: var(--color-text-primary);
    margin: 0;
  }

  .line {
    width: 100%;
    max-width: 300px;
    height: 4px;
    border-radius: var(--border-radius);
    background: var(--accent-regular);
  }

  .container {
    width: 100%;
    padding: 0 var(--spacing-4, 1rem);
    display: grid;
    grid-template-columns: 1.1fr 0.9fr;
    justify-content: space-between;
    text-align: left;
    gap: clamp(1.5rem, 4vw, 3rem);
  }

  .col {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: flex-start;
  }

  .description {
    color: var(--color-text-secondary);
    white-space: pre-line;
  }

  .image-container {
    position: relative;
    display: inline-block;
    border-radius: var(--border-radius);
    overflow: hidden;
  }

  /* l’image reste en dessous */
  .image-container img {
    display: block;
    width: 100%;
    height: auto;
    max-width: 300px;
    aspect-ratio: 3 / 4;
    object-fit: cover;
    position: relative;
    z-index: 0;
  }

  @media (max-width: 900px) {
    .container {
      grid-template-columns: 1fr;
    }
  }
</style>
